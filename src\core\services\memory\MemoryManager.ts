import * as fs from "fs/promises"
import * as path from "path"
import { v4 as uuidv4 } from "uuid"
import { Anthropic } from "@anthropic-ai/sdk"
import {
	MemoryEntry,
	MemoryCategory,
	MemoryStorage,
	MemoryConfig,
	MemoryClassificationResult,
	DEFAULT_MEMORY_CONFIG,
	MEMORY_CATEGORIES,
} from "./types"
import { MEMORY_CLASSIFICATION_SYSTEM_PROMPT, generateMemoryClassificationPrompt, generateDeduplicationPrompt } from "./prompts"
import { ApiHandler } from "@api/index"
import { Logger } from "@services/logging/Logger"

/**
 * Memory Manager for handling user input classification and storage
 */
export class MemoryManager {
	private config: MemoryConfig
	private memoriesFilePath: string
	private storage: MemoryStorage
	private processingQueue: Map<string, NodeJS.Timeout> = new Map()
	private apiHandler: ApiHandler
	private isDisposed = false

	constructor(workspaceRoot: string, apiHandler: ApiHandler, config: Partial<MemoryConfig> = {}) {
		this.config = { ...DEFAULT_MEMORY_CONFIG, ...config }
		this.memoriesFilePath = path.join(workspaceRoot, ".clinerules", "memories.md")
		this.apiHandler = apiHandler
		this.storage = this.initializeStorage()
		this.loadMemories()
	}

	/**
	 * Initialize empty storage structure
	 */
	private initializeStorage(): MemoryStorage {
		const storage = {} as MemoryStorage
		Object.values(MemoryCategory).forEach((category) => {
			storage[category] = []
		})
		return storage
	}

	/**
	 * Process user input for memory extraction
	 */
	public async processUserInput(input: string, context?: string): Promise<void> {
		console.log("🧠 MemoryManager.processUserInput() called")
		console.log("🧠 Config enabled:", this.config.enabled)
		console.log("🧠 Input length:", input.length, "Min length:", this.config.minInputLength)
		console.log("🧠 Memories file path:", this.memoriesFilePath)

		if (!this.config.enabled || input.length < this.config.minInputLength) {
			console.log("🧠 Skipping processing - disabled or input too short")
			return
		}

		// Debounce processing to avoid excessive API calls
		const inputId = this.generateInputId(input)
		console.log("🧠 Input ID for debouncing:", inputId)
		console.log("🧠 Current processing queue size:", this.processingQueue.size)

		// Clear existing timeout for this input
		const existingTimeout = this.processingQueue.get(inputId)
		if (existingTimeout) {
			console.log("🧠 Clearing existing timeout for input - this will reset the debounce timer!")
			clearTimeout(existingTimeout)
		}

		// Set new timeout
		console.log("🧠 Setting timeout for", this.config.debounceMs, "ms")
		const startTime = Date.now()
		const timeout = setTimeout(async () => {
			try {
				const actualDelay = Date.now() - startTime
				console.log("🧠 Timeout triggered after", actualDelay, "ms (expected:", this.config.debounceMs, "ms)")
				console.log("🧠 Starting analysis for input:", input.substring(0, 50) + "...")
				await this.analyzeAndStoreInput(input, context)
				this.processingQueue.delete(inputId)
				console.log("🧠 Processing completed successfully, queue size:", this.processingQueue.size)
			} catch (error) {
				console.error("🧠 Failed to process memory input:", error)
				console.error("🧠 Error details:", error.message)
				console.error("🧠 Error stack:", error.stack)
				Logger.error("Failed to process memory input:", error)
				this.processingQueue.delete(inputId)
			}
		}, this.config.debounceMs)

		this.processingQueue.set(inputId, timeout)
		console.log("🧠 Timeout set, queue size now:", this.processingQueue.size)
		console.log("🧠 Timeout will trigger at:", new Date(Date.now() + this.config.debounceMs).toISOString())
	}

	/**
	 * Analyze input and store if valuable
	 */
	private async analyzeAndStoreInput(input: string, context?: string): Promise<void> {
		try {
			console.log("🧠 Starting analysis of input:", input.substring(0, 50) + "...")
			const classification = await this.classifyInput(input, context)
			console.log("🧠 Classification result:", classification)

			if (classification.confidence < this.config.confidenceThreshold) {
				console.log(
					`🧠 Classification confidence too low: ${classification.confidence} < ${this.config.confidenceThreshold}`,
				)
				Logger.info(`Memory classification confidence too low: ${classification.confidence}`)
				return
			}

			// Check for duplicates
			const existingEntries = this.storage[classification.category]
			console.log("🧠 Checking duplicates against", existingEntries.length, "existing entries")
			const isDuplicate = await this.checkDuplicate(classification.summary, existingEntries)

			if (isDuplicate) {
				console.log("🧠 Entry is duplicate, skipping")
				Logger.info("Memory entry is duplicate, skipping")
				return
			}

			// Create and store memory entry
			const memoryEntry: MemoryEntry = {
				id: uuidv4(),
				category: classification.category,
				summary: classification.summary,
				originalInput: input,
				timestamp: Date.now(),
				confidence: classification.confidence,
			}

			console.log("🧠 Adding memory entry:", memoryEntry)
			await this.addMemoryEntry(memoryEntry)
			console.log("🧠 Memory entry stored successfully!")
			Logger.info(`Stored memory entry: ${classification.category} - ${classification.summary}`)
		} catch (error) {
			console.error("🧠 Failed to analyze and store input:", error)
			Logger.error("Failed to analyze and store input:", error)
		}
	}

	/**
	 * Classify user input using AI with timeout protection
	 */
	private async classifyInput(input: string, context?: string): Promise<MemoryClassificationResult> {
		console.log("🧠 Starting AI classification with timeout protection...")
		const userPrompt = generateMemoryClassificationPrompt(input, context)
		console.log("🧠 Generated prompt length:", userPrompt.length)

		const messages: Anthropic.Messages.MessageParam[] = [{ role: "user", content: userPrompt }]

		console.log("🧠 Calling API handler with 15 second timeout...")

		// Create timeout promise with cleanup
		let timeoutId: NodeJS.Timeout | null = null
		const timeoutPromise = new Promise<never>((_, reject) => {
			timeoutId = setTimeout(() => {
				console.error("🧠 API call timed out after 15 seconds")
				reject(new Error("AI classification API call timed out"))
			}, 15000) // 15 second timeout
		})

		// Create API call promise
		const apiCallPromise = this.performApiCall(messages)

		// Race between API call and timeout
		try {
			const result = await Promise.race([apiCallPromise, timeoutPromise])
			// Clear timeout if API call succeeded
			if (timeoutId) {
				clearTimeout(timeoutId)
			}
			console.log("🧠 Final classification result:", result)
			return result
		} catch (error) {
			// Clear timeout on error as well
			if (timeoutId) {
				clearTimeout(timeoutId)
			}
			console.error("🧠 AI classification failed:", error)
			// Return a default result to prevent blocking
			return {
				category: MemoryCategory.GENERAL,
				summary: input.substring(0, 100),
				confidence: 0.1,
			}
		}
	}

	/**
	 * Perform the actual API call
	 */
	private async performApiCall(messages: Anthropic.Messages.MessageParam[]): Promise<MemoryClassificationResult> {
		const stream = this.apiHandler.createMessage(MEMORY_CLASSIFICATION_SYSTEM_PROMPT, messages)

		let content = ""
		console.log("🧠 Reading API response stream...")

		// Add timeout for stream reading as well
		const streamTimeout = setTimeout(() => {
			console.error("🧠 Stream reading timed out")
			throw new Error("Stream reading timeout")
		}, 10000) // 10 second timeout for stream

		try {
			for await (const chunk of stream) {
				if (chunk.type === "text") {
					content += chunk.text
				}
			}
			clearTimeout(streamTimeout)
		} catch (error) {
			clearTimeout(streamTimeout)
			throw error
		}

		console.log("🧠 API response received, length:", content.length)
		console.log("🧠 API response content:", content.substring(0, 200) + "...")

		// Parse JSON response
		const jsonMatch = content.match(/```json\n([\s\S]*?)\n```/)

		if (!jsonMatch) {
			console.error("🧠 No JSON found in response:", content)
			throw new Error("Invalid response format from AI classification")
		}

		console.log("🧠 Found JSON in response:", jsonMatch[1])
		const result = JSON.parse(jsonMatch[1]) as MemoryClassificationResult

		// Validate category
		if (!Object.values(MemoryCategory).includes(result.category)) {
			console.log("🧠 Invalid category, defaulting to GENERAL:", result.category)
			result.category = MemoryCategory.GENERAL
		}

		return result
	}

	/**
	 * Check if entry is duplicate
	 */
	private async checkDuplicate(newSummary: string, existingEntries: MemoryEntry[]): Promise<boolean> {
		if (existingEntries.length === 0) {
			return false
		}

		const existingSummaries = existingEntries.map((entry) => entry.summary)
		const prompt = generateDeduplicationPrompt(newSummary, existingSummaries)

		try {
			const messages: Anthropic.Messages.MessageParam[] = [{ role: "user", content: prompt }]

			const stream = this.apiHandler.createMessage("", messages)

			let content = ""
			for await (const chunk of stream) {
				if (chunk.type === "text") {
					content += chunk.text
				}
			}

			const jsonMatch = content.match(/```json\n([\s\S]*?)\n```/)
			if (jsonMatch) {
				const result = JSON.parse(jsonMatch[1])
				return result.isDuplicate === true
			}
		} catch (error) {
			Logger.error("Failed to check duplicate:", error)
		}

		return false
	}

	/**
	 * Add memory entry to storage
	 */
	private async addMemoryEntry(entry: MemoryEntry): Promise<void> {
		const categoryEntries = this.storage[entry.category]

		// Add new entry
		categoryEntries.unshift(entry)

		// Limit entries per category
		if (categoryEntries.length > this.config.maxEntriesPerCategory) {
			categoryEntries.splice(this.config.maxEntriesPerCategory)
		}

		// Save to file
		await this.saveMemories()
	}

	/**
	 * Generate unique ID for input (for debouncing)
	 */
	private generateInputId(input: string): string {
		return Buffer.from(input).toString("base64").slice(0, 16)
	}

	/**
	 * Load memories from file
	 */
	private async loadMemories(): Promise<void> {
		try {
			const content = await fs.readFile(this.memoriesFilePath, "utf-8")
			this.parseMemoriesFromMarkdown(content)
		} catch (error) {
			// File doesn't exist, start with empty storage
			Logger.info("Memories file not found, starting with empty storage")
		}
	}

	/**
	 * Save memories to markdown file with timeout protection
	 */
	private async saveMemories(): Promise<void> {
		console.log("🧠 Generating markdown content...")
		const markdown = this.generateMarkdown()
		console.log("🧠 Generated markdown length:", markdown.length)

		// Ensure directory exists with timeout
		const dir = path.dirname(this.memoriesFilePath)
		console.log("🧠 Ensuring directory exists:", dir)

		try {
			// Add timeout for directory creation
			const dirPromise = fs.mkdir(dir, { recursive: true })
			const dirTimeout = new Promise<never>((_, reject) => {
				setTimeout(() => reject(new Error("Directory creation timeout")), 5000)
			})

			await Promise.race([dirPromise, dirTimeout])
			console.log("🧠 Directory created/verified successfully")
		} catch (error) {
			console.error("🧠 Failed to create directory:", error)
			throw error
		}

		console.log("🧠 Writing file to:", this.memoriesFilePath)
		try {
			// Add timeout for file writing
			const writePromise = fs.writeFile(this.memoriesFilePath, markdown, "utf-8")
			const writeTimeout = new Promise<never>((_, reject) => {
				setTimeout(() => reject(new Error("File write timeout")), 5000)
			})

			await Promise.race([writePromise, writeTimeout])
			console.log("🧠 File written successfully!")
		} catch (error) {
			console.error("🧠 Failed to write file:", error)
			throw error
		}
	}

	/**
	 * Generate markdown content from storage
	 */
	private generateMarkdown(): string {
		const lines: string[] = [
			"# Cline Memories",
			"",
			"这个文件记录了用户的偏好、需求和习惯，帮助Cline更好地理解用户的开发风格。",
			"",
			`最后更新：${new Date().toLocaleString("zh-CN")}`,
			"",
		]

		Object.entries(this.storage).forEach(([category, entries]) => {
			if (entries.length === 0) {
				return
			}

			const metadata = MEMORY_CATEGORIES[category as MemoryCategory]
			lines.push(`## ${metadata.icon} ${metadata.displayName}`)
			lines.push("")
			lines.push(metadata.description)
			lines.push("")

			entries.forEach((entry: MemoryEntry) => {
				lines.push(`- ${entry.summary}`)
			})
			lines.push("")
		})

		return lines.join("\n")
	}

	/**
	 * Parse memories from markdown content
	 */
	private parseMemoriesFromMarkdown(content: string): void {
		// Simple parsing - look for category sections and bullet points
		const lines = content.split("\n")
		let currentCategory: MemoryCategory | null = null

		for (const line of lines) {
			// Check for category headers
			const categoryMatch = line.match(/^## .+ (.+)$/)
			if (categoryMatch) {
				const categoryName = Object.keys(MEMORY_CATEGORIES).find(
					(key) => MEMORY_CATEGORIES[key as MemoryCategory].displayName === categoryMatch[1].trim(),
				)
				currentCategory = (categoryName as MemoryCategory) || null
				continue
			}

			// Check for bullet points
			if (currentCategory && line.startsWith("- ")) {
				const summary = line.slice(2).trim()
				if (summary) {
					const entry: MemoryEntry = {
						id: uuidv4(),
						category: currentCategory,
						summary,
						originalInput: "", // Not stored in markdown
						timestamp: Date.now(),
						confidence: 1.0, // Assume high confidence for existing entries
					}
					this.storage[currentCategory].push(entry)
				}
			}
		}
	}

	/**
	 * Get all memories for a category
	 */
	public getMemoriesByCategory(category: MemoryCategory): MemoryEntry[] {
		return [...this.storage[category]]
	}

	/**
	 * Get all memories
	 */
	public getAllMemories(): MemoryStorage {
		return { ...this.storage }
	}

	/**
	 * Clear all memories
	 */
	public async clearMemories(): Promise<void> {
		this.storage = this.initializeStorage()
		await this.saveMemories()
	}

	/**
	 * Update configuration
	 */
	public updateConfig(newConfig: Partial<MemoryConfig>): void {
		this.config = { ...this.config, ...newConfig }
	}

	/**
	 * Cleanup resources
	 */
	public dispose(): void {
		// Clear all pending timeouts
		this.processingQueue.forEach((timeout) => clearTimeout(timeout))
		this.processingQueue.clear()
	}
}
