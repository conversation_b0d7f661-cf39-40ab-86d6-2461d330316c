import { useExtensionState } from "@/context/ExtensionStateContext"
import { useQaxAuth } from "@/hooks/useQaxAuth"
import { ModelsServiceClient } from "@/services/grpc-client"
import { openAiModelInfoSaneDefaults } from "@shared/api"
import { OpenAiModelsRequest } from "@shared/proto/models"
import { VSCodeButton, VSCodeCheckbox, VSCodeDropdown, VSCodeOption, VSCodeProgressRing } from "@vscode/webview-ui-toolkit/react"
import { memo, useCallback, useEffect, useState } from "react"
import { DebouncedTextField } from "../common/DebouncedTextField"
import { ModelInfoView } from "../common/ModelInfoView"
import { useApiConfigurationHandlers } from "../utils/useApiConfigurationHandlers"

interface QaxCodegenProviderProps {
	showModelOptions: boolean
	isPopup?: boolean
}

const QaxCodegenProvider = ({ showModelOptions, isPopup }: QaxCodegenProviderProps) => {
	const { apiConfiguration } = useExtensionState()
	const { handleFieldChange } = useApiConfigurationHandlers()

	// 使用共享的 QAX 认证 Hook
	const { qaxUser, isAuthenticated, isLoading: authLoading, handleLogin } = useQaxAuth()

	const [models, setModels] = useState<[string, string][]>([])
	const [loading, setLoading] = useState(false)
	const [modelConfigurationSelected, setModelConfigurationSelected] = useState(false)

	// Get the normalized configuration for QAX Codegen
	const selectedModelId = apiConfiguration?.qaxCodegenModelId || ""
	const selectedModelInfo = apiConfiguration?.qaxCodegenModelInfo || openAiModelInfoSaneDefaults

	// Debug logging
	console.log('QaxCodegenProvider Debug:', {
		selectedModelId,
		models,
		modelsLength: models.length,
		modelsIncludesSelected: models.some(([modelId]) => modelId === selectedModelId),
		qaxCodegenModelId: apiConfiguration?.qaxCodegenModelId
	})

	// Helper function to update model info
	const updateModelInfo = useCallback(
		(updater: (modelInfo: any) => any) => {
			const currentModelInfo = apiConfiguration?.qaxCodegenModelInfo || { ...openAiModelInfoSaneDefaults }
			const updatedModelInfo = updater(currentModelInfo)
			handleFieldChange("qaxCodegenModelInfo", updatedModelInfo)
		},
		[apiConfiguration?.qaxCodegenModelInfo, handleFieldChange],
	)

	// 获取模型列表
	const fetchModels = useCallback(async () => {
		setLoading(true)
		try {
			// 调用 QAX Codegen gRPC 服务获取模型
			// getQaxCodegenModels 已经处理了所有错误情况和默认模型回退
			const response = await ModelsServiceClient.getQaxCodegenModels(
				OpenAiModelsRequest.create({
					// QAX Codegen 使用 JWT token，不需要传递 API key
					apiKey: "",
					baseUrl: "",
				}),
			)

			// response.values 总是有值，因为 gRPC 服务已经处理了回退
			setModels(response.values.map((modelId: string) => [modelId, modelId]))
		} catch (error) {
			console.error("Failed to fetch QAX Codegen models:", error)
			// 这种情况理论上不会发生，因为 gRPC 服务已经处理了所有错误
			setModels([])
		} finally {
			setLoading(false)
		}
	}, [])

	// 组件挂载时获取模型列表 - 遵循 Cline 项目的简单模式，不使用 AbortController
	useEffect(() => {
		fetchModels()
	}, [fetchModels]) // 包含 fetchModels 依赖项，遵循其他 Provider 组件的模式

	// QAX Account Info Card (参考 QAXAccountCard)
	const renderQaxAccountCard = () => {
		if (!isAuthenticated) {
			return (
				<div style={{ marginBottom: 14, marginTop: 4 }}>
					<div
						style={{
							padding: "16px",
							border: "1px solid var(--vscode-input-border)",
							borderRadius: "4px",
							backgroundColor: "var(--vscode-input-background)",
							textAlign: "center",
						}}>
						<h3
							style={{
								margin: "0 0 16px 0",
								fontSize: "16px",
								fontWeight: 600,
								color: "var(--vscode-foreground)",
							}}>
							QAX Codegen
						</h3>
						<VSCodeButton onClick={handleLogin} disabled={authLoading}>
							{authLoading ? (
								<div style={{ display: "flex", alignItems: "center", gap: "8px" }}>
									<VSCodeProgressRing style={{ width: "16px", height: "16px" }} />
									Connecting...
								</div>
							) : (
								"Connect to QAX Account"
							)}
						</VSCodeButton>
					</div>
					<p
						style={{
							fontSize: "12px",
							marginTop: 8,
							color: "var(--vscode-descriptionForeground)",
						}}>
						QAX Codegen uses your QAX account credentials for authentication. No additional API key is required.
					</p>
				</div>
			)
		}

		return (
			<div style={{ marginBottom: 14, marginTop: 4 }}>
				<div
					style={{
						padding: "12px",
						border: "1px solid var(--vscode-input-border)",
						borderRadius: "4px",
						backgroundColor: "var(--vscode-input-background)",
						display: "flex",
						justifyContent: "space-between",
						alignItems: "center",
					}}>
					<div>
						<h3 style={{ margin: "0 0 4px 0", fontSize: "16px", fontWeight: 600, color: "var(--vscode-foreground)" }}>
							QAX Account
						</h3>
						<p style={{ margin: 0, fontSize: "14px", color: "var(--vscode-descriptionForeground)" }}>
							{qaxUser?.email || "No email"}
						</p>
					</div>
				</div>
			</div>
		)
	}

	return (
		<div>
			{/* QAX Account Info Card */}
			{renderQaxAccountCard()}

			{/* 始终显示模型选择，但在未认证时禁用 */}
			{showModelOptions && (
				<>
					{/* Model Selection */}
					<label htmlFor="qax-codegen-model">
						<span style={{ fontWeight: 500 }}>Model</span>
					</label>
					<VSCodeDropdown
						key={`qax-codegen-model-dropdown-${models.length}-${selectedModelId}`}
						id="qax-codegen-model"
						value={models.length > 0 && selectedModelId && models.some(([modelId]) => modelId === selectedModelId) ? selectedModelId : ""}
						style={{ width: "100%", marginBottom: 10 }}
						onChange={(e) => {
							const value = (e.target as HTMLSelectElement)?.value
							handleFieldChange("qaxCodegenModelId", value)
						}}
						disabled={loading}>
						<VSCodeOption value="">Select a model...</VSCodeOption>
						{loading ? (
							<VSCodeOption value="" disabled>
								Loading models...
							</VSCodeOption>
						) : models.length > 0 ? (
							models.map(([modelId, displayName]) => (
								<VSCodeOption key={modelId} value={modelId}>
									{displayName}
								</VSCodeOption>
							))
						) : (
							<VSCodeOption value="" disabled>
								No models available
							</VSCodeOption>
						)}
					</VSCodeDropdown>

					{/* Model Configuration Section (类似 OpenAI Compatible) - 只在认证时显示 */}
					{isAuthenticated && (
						<div
							style={{
								color: "var(--vscode-descriptionForeground)",
								display: "flex",
								margin: "10px 0",
								cursor: "pointer",
								alignItems: "center",
							}}
							onClick={() => setModelConfigurationSelected((val) => !val)}>
							<span
								className={`codicon ${modelConfigurationSelected ? "codicon-chevron-down" : "codicon-chevron-right"}`}
								style={{
									marginRight: "4px",
								}}></span>
							<span
								style={{
									fontWeight: 700,
									textTransform: "uppercase",
								}}>
								Model Configuration
							</span>
						</div>
					)}

					{modelConfigurationSelected && (
						<>
							<VSCodeCheckbox
								checked={!!apiConfiguration?.qaxCodegenModelInfo?.supportsImages}
								onChange={(e: any) => {
									const isChecked = e.target.checked === true
									updateModelInfo((modelInfo) => ({ ...modelInfo, supportsImages: isChecked }))
								}}>
								Supports Images
							</VSCodeCheckbox>

							<VSCodeCheckbox
								checked={!!apiConfiguration?.qaxCodegenModelInfo?.isR1FormatRequired}
								onChange={(e: any) => {
									const isChecked = e.target.checked === true
									updateModelInfo((modelInfo) => ({ ...modelInfo, isR1FormatRequired: isChecked }))
								}}>
								Enable R1 messages format
							</VSCodeCheckbox>

							<div style={{ display: "flex", gap: 10, marginTop: "5px" }}>
								<DebouncedTextField
									initialValue={
										apiConfiguration?.qaxCodegenModelInfo?.contextWindow
											? apiConfiguration.qaxCodegenModelInfo.contextWindow.toString()
											: (openAiModelInfoSaneDefaults.contextWindow?.toString() ?? "")
									}
									style={{ flex: 1 }}
									onChange={(value) => {
										updateModelInfo((modelInfo) => ({ ...modelInfo, contextWindow: Number(value) }))
									}}>
									<span style={{ fontWeight: 500 }}>Context Window Size</span>
								</DebouncedTextField>

								<DebouncedTextField
									initialValue={
										apiConfiguration?.qaxCodegenModelInfo?.maxTokens
											? apiConfiguration.qaxCodegenModelInfo.maxTokens.toString()
											: (openAiModelInfoSaneDefaults.maxTokens?.toString() ?? "")
									}
									style={{ flex: 1 }}
									onChange={(value) => {
										updateModelInfo((modelInfo) => ({ ...modelInfo, maxTokens: Number(value) }))
									}}>
									<span style={{ fontWeight: 500 }}>Max Output Tokens</span>
								</DebouncedTextField>
							</div>

							<div style={{ display: "flex", gap: 10, marginTop: "5px" }}>
								<DebouncedTextField
									initialValue={
										apiConfiguration?.qaxCodegenModelInfo?.temperature
											? apiConfiguration.qaxCodegenModelInfo.temperature.toString()
											: (openAiModelInfoSaneDefaults.temperature?.toString() ?? "")
									}
									onChange={(value) => {
										const shouldPreserveFormat =
											value.endsWith(".") || (value.includes(".") && value.endsWith("0"))
										const temperature =
											value === ""
												? openAiModelInfoSaneDefaults.temperature
												: shouldPreserveFormat
													? (value as any)
													: parseFloat(value)

										updateModelInfo((modelInfo) => ({ ...modelInfo, temperature }))
									}}>
									<span style={{ fontWeight: 500 }}>Temperature</span>
								</DebouncedTextField>
							</div>
						</>
					)}

					{showModelOptions && (
						<ModelInfoView selectedModelId={selectedModelId} modelInfo={selectedModelInfo} isPopup={isPopup} />
					)}
				</>
			)}

			<p
				style={{
					fontSize: "12px",
					marginTop: 3,
					color: "var(--vscode-descriptionForeground)",
				}}>
				QAX Codegen provides access to advanced AI models through your QAX account.
			</p>
		</div>
	)
}

export default memo(QaxCodegenProvider)
