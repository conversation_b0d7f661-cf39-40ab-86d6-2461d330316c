import { ModelsServiceClient } from "@/services/grpc-client"
import { ApiConfiguration } from "@shared/api"
import { OpenAiModelsRequest } from "@shared/proto/models"
import { VSCodeDropdown, VSCodeOption, VSCodeTextField } from "@vscode/webview-ui-toolkit/react"
import { memo, useCallback, useEffect, useState } from "react"
import { BaseUrlField } from "../common/BaseUrlField"
import { normalizeApiConfiguration } from "../utils/providerUtils"

interface QaxProviderProps {
	apiConfiguration: ApiConfiguration
	handleInputChange: (field: keyof ApiConfiguration) => (event: any) => void
	showModelOptions: boolean
	isPopup?: boolean
}

const QaxProvider = ({ apiConfiguration, handleInputChange, showModelOptions }: QaxProviderProps) => {
	const [models, setModels] = useState<string[]>([])
	const [loading, setLoading] = useState(false)

	// Get the normalized configuration
	const { selectedModelId } = normalizeApiConfiguration(apiConfiguration)
	
	// Fetch QAX models using gRPC
	const fetchQaxModels = useCallback(async () => {
		if (!apiConfiguration?.qaxApiKey) {
			setModels([])
			return
		}

		setLoading(true)
		try {
			const response = await ModelsServiceClient.getQaxModels(
				OpenAiModelsRequest.create({
					apiKey: apiConfiguration.qaxApiKey,
					baseUrl: apiConfiguration.qaxApiBaseUrl || "https://aip.b.qianxin-inc.cn/v1",
				}),
			)
			if (response && response.values) {
				setModels(response.values)
			}
		} catch (error) {
			console.error("Failed to fetch QAX models:", error)
			setModels([])
		} finally {
			setLoading(false)
		}
	}, [apiConfiguration?.qaxApiKey, apiConfiguration?.qaxApiBaseUrl])

	useEffect(() => {
		fetchQaxModels()
	}, [fetchQaxModels])

	return (
		<div>
			<VSCodeTextField
				value={apiConfiguration?.qaxApiKey || ""}
				style={{ width: "100%" }}
				type="password"
				onInput={handleInputChange("qaxApiKey")}
				placeholder="Enter API Key...">
				<span style={{ fontWeight: 500 }}>QAX API Key</span>
			</VSCodeTextField>
			<p
				style={{
					fontSize: "12px",
					marginTop: 3,
					color: "var(--vscode-descriptionForeground)",
				}}>
				This key is stored locally and only used to make API requests from this extension.
			</p>

			<BaseUrlField
				initialValue={apiConfiguration?.qaxApiBaseUrl || ""}
				onChange={(value) => handleInputChange("qaxApiBaseUrl")({ target: { value } })}
				label="Use custom QAX API base URL"
				placeholder="https://aip.b.qianxin-inc.cn/v1"
			/>

			{showModelOptions && (
				<>
					<label htmlFor="qax-model">
						<span style={{ fontWeight: 500 }}>Model</span>
					</label>
					<VSCodeDropdown
						key="qax-model-dropdown"
						id="qax-model"
						value={selectedModelId}
						style={{ width: "100%" }}
						onChange={handleInputChange("qaxModelId")}
						disabled={loading || models.length === 0}>
						<VSCodeOption value="">Select a model...</VSCodeOption>
						{loading ? (
							<VSCodeOption value="" disabled>
								Loading models...
							</VSCodeOption>
						) : models.length > 0 ? (
							models.map((modelId) => (
								<VSCodeOption key={modelId} value={modelId}>
									{modelId}
								</VSCodeOption>
							))
						) : (
							<VSCodeOption value="" disabled>
								No models available
							</VSCodeOption>
						)}
					</VSCodeDropdown>
				</>
			)}
			<p
				style={{
					fontSize: "12px",
					marginTop: 3,
					color: "var(--vscode-descriptionForeground)",
				}}>
				Select the model to use for requests.
			</p>
		</div>
	)
}

export default memo(QaxProvider)
