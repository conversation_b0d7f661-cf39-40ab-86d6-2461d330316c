import { Controller } from ".."
import { Empty } from "../../../shared/proto/common"
import { NewTaskRequest } from "../../../shared/proto/task"

/**
 * Creates a new task with the given text and optional images
 * @param controller The controller instance
 * @param request The new task request containing text and optional images
 * @returns Empty response
 */
export async function newTask(controller: Controller, request: NewTaskRequest): Promise<Empty> {
	// Create the task first, then process memory after initialization
	await controller.initTask(request.text, request.images, request.files)

	// Process the first user message for memory after task and memory service are initialized
	if (request.text) {
		try {
			console.log("🧠 Processing first user message for memory:", request.text.substring(0, 100) + "...")
			const { getMemoryService } = await import("../../services/memory")
			const memoryService = getMemoryService()
			if (memoryService) {
				console.log("🧠 Memory service found, processing first message...")
				// Process in background without blocking the main flow
				memoryService.processUserInput(request.text).catch((error) => {
					console.error("🧠 Failed to process first message for memory:", error)
				})
			} else {
				console.warn("🧠 Memory service not available after task initialization")
			}
		} catch (error) {
			console.error("🧠 Failed to load memory service for first message:", error)
		}
	}

	return Empty.create()
}
