/**
 * Memory categories for user input classification
 */
export enum MemoryCategory {
	FEATURE_REQUIREMENTS = "feature_requirements",
	TOOL_PREFERENCES = "tool_preferences",
	TECH_STACK = "tech_stack",
	CODE_STYLE = "code_style",
	WORKFLOW = "workflow",
	PROBLEM_SOLVING = "problem_solving",
	GENERAL = "general",
}

/**
 * Memory entry representing a classified and summarized user input
 */
export interface MemoryEntry {
	id: string
	category: MemoryCategory
	summary: string
	originalInput: string
	timestamp: number
	confidence: number // 0-1, confidence in classification
}

/**
 * Memory classification result from AI analysis
 */
export interface MemoryClassificationResult {
	category: MemoryCategory
	summary: string
	confidence: number
	reasoning?: string
}

/**
 * Configuration for memory generation
 */
export interface MemoryConfig {
	enabled: boolean
	minInputLength: number // Minimum input length to process
	maxEntriesPerCategory: number // Maximum entries to keep per category
	confidenceThreshold: number // Minimum confidence to save entry
	debounceMs: number // Debounce time for processing
}

/**
 * Memory storage structure for the markdown file
 */
export interface MemoryStorage {
	[MemoryCategory.FEATURE_REQUIREMENTS]: MemoryEntry[]
	[MemoryCategory.TOOL_PREFERENCES]: MemoryEntry[]
	[MemoryCategory.TECH_STACK]: MemoryEntry[]
	[MemoryCategory.CODE_STYLE]: MemoryEntry[]
	[MemoryCategory.WORKFLOW]: MemoryEntry[]
	[MemoryCategory.PROBLEM_SOLVING]: MemoryEntry[]
	[MemoryCategory.GENERAL]: MemoryEntry[]
}

/**
 * Category metadata for display and organization
 */
export interface CategoryMetadata {
	displayName: string
	description: string
	icon: string
	examples: string[]
}

/**
 * Complete category configuration
 */
export const MEMORY_CATEGORIES: Record<MemoryCategory, CategoryMetadata> = {
	[MemoryCategory.FEATURE_REQUIREMENTS]: {
		displayName: "功能需求",
		description: "用户想要实现的具体功能和特性",
		icon: "🎯",
		examples: ["我想要添加一个登录功能", "需要实现文件上传功能", "希望有数据可视化图表"],
	},
	[MemoryCategory.TOOL_PREFERENCES]: {
		displayName: "工具偏好",
		description: "用户喜欢使用的开发工具、库和框架",
		icon: "🔧",
		examples: ["我喜欢使用React", "偏好使用TypeScript", "习惯用VS Code开发"],
	},
	[MemoryCategory.TECH_STACK]: {
		displayName: "技术栈",
		description: "用户希望使用的编程语言、框架和技术",
		icon: "💻",
		examples: ["使用Node.js后端", "前端用Vue.js", "数据库选择PostgreSQL"],
	},
	[MemoryCategory.CODE_STYLE]: {
		displayName: "代码风格",
		description: "用户的编程习惯、代码规范和风格偏好",
		icon: "📝",
		examples: ["喜欢函数式编程", "使用ESLint规范", "偏好简洁的代码注释"],
	},
	[MemoryCategory.WORKFLOW]: {
		displayName: "工作流程",
		description: "用户的开发流程、方法论和项目管理偏好",
		icon: "⚡",
		examples: ["使用Git Flow工作流", "喜欢TDD开发方式", "偏好敏捷开发方法"],
	},
	[MemoryCategory.PROBLEM_SOLVING]: {
		displayName: "问题解决",
		description: "用户遇到的常见问题、解决方案和调试方法",
		icon: "🔍",
		examples: ["经常遇到跨域问题", "性能优化的经验", "调试技巧和方法"],
	},
	[MemoryCategory.GENERAL]: {
		displayName: "一般信息",
		description: "其他不属于特定分类的有用信息",
		icon: "📋",
		examples: ["项目背景信息", "团队协作方式", "需要参考的文档"],
	},
}

/**
 * Default memory configuration
 */
export const DEFAULT_MEMORY_CONFIG: MemoryConfig = {
	enabled: true,
	minInputLength: 10,
	maxEntriesPerCategory: 20,
	confidenceThreshold: 0.6,
	debounceMs: 2000,
}
