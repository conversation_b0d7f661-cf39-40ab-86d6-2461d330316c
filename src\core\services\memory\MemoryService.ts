import * as vscode from "vscode"
import { MemoryManager } from "./MemoryManager"
import { MemoryConfig } from "./types"
import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "@api/index"
import { Logger } from "@services/logging/Logger"

/**
 * Memory Service for managing user input memories
 * Integrates with the VS Code extension lifecycle
 */
export class MemoryService {
	private memoryManager: MemoryManager | null = null
	private isEnabled: boolean = true
	private disposables: vscode.Disposable[] = []
	private cachedWorkspaceRoot: string | null = null
	private workspaceRootCacheTime: number = 0
	private readonly CACHE_DURATION = 30000 // 30 seconds cache
	private isInitializing: boolean = false
	private initializationPromise: Promise<void> | null = null

	constructor(
		private context: vscode.ExtensionContext,
		private getApiHandler: () => ApiHandler | null,
		private getWorkspaceRoot: () => Promise<string>,
	) {
		this.loadConfiguration()
		this.setupConfigurationWatcher()
		this.setupWorkspaceWatcher()
		this.setupApiHandlerMonitoring()
	}

	/**
	 * Get workspace root with caching to avoid frequent gRPC calls
	 */
	private async getCachedWorkspaceRoot(): Promise<string> {
		console.log("🧠 getCachedWorkspaceRoot() called")
		const now = Date.now()

		// Return cached value if still valid
		if (this.cachedWorkspaceRoot && now - this.workspaceRootCacheTime < this.CACHE_DURATION) {
			console.log("🧠 Using cached workspace root:", this.cachedWorkspaceRoot)
			return this.cachedWorkspaceRoot
		}

		// Get fresh value and cache it
		console.log("🧠 Fetching fresh workspace root via getWorkspaceRoot()...")
		try {
			const workspaceRoot = await this.getWorkspaceRoot()
			console.log("🧠 getWorkspaceRoot() returned:", workspaceRoot)
			this.cachedWorkspaceRoot = workspaceRoot
			this.workspaceRootCacheTime = now
			console.log("🧠 Cached new workspace root:", workspaceRoot)
			return workspaceRoot
		} catch (error) {
			console.error("🧠 Error in getWorkspaceRoot():", error)
			return ""
		}
	}

	/**
	 * Clear workspace root cache (called when workspace changes)
	 */
	private clearWorkspaceCache(): void {
		console.log("🧠 Clearing workspace root cache")
		this.cachedWorkspaceRoot = null
		this.workspaceRootCacheTime = 0
	}

	/**
	 * Initialize the memory service (with protection against concurrent initialization)
	 */
	public async initialize(): Promise<void> {
		console.log("🧠 MemoryService.initialize() called, enabled:", this.isEnabled)

		if (!this.isEnabled) {
			console.log("🧠 Memory service is disabled")
			return
		}

		// If already ready, no need to initialize
		if (this.isReady()) {
			console.log("🧠 Memory service already initialized and ready")
			return
		}

		// If currently initializing, wait for the existing initialization
		if (this.isInitializing && this.initializationPromise) {
			console.log("🧠 Memory service is already initializing, waiting for completion...")
			await this.initializationPromise
			return
		}

		// Start initialization
		this.isInitializing = true
		this.initializationPromise = this.performInitialization()

		try {
			await this.initializationPromise
		} finally {
			this.isInitializing = false
			this.initializationPromise = null
		}
	}

	/**
	 * Perform the actual initialization work
	 */
	private async performInitialization(): Promise<void> {
		try {
			console.log("🧠 Step 1: Getting API handler...")
			const apiHandler = this.getApiHandler()
			console.log("🧠 Step 1 result - API handler available:", !!apiHandler)
			if (!apiHandler) {
				console.log("🧠 Step 1 FAILED - API handler is null/undefined")
				Logger.warn("API handler not available, memory service disabled")
				return
			}
			console.log("🧠 Step 1 SUCCESS - API handler is available")

			console.log("🧠 Step 2: Getting workspace root...")
			const workspaceRoot = await this.getCachedWorkspaceRoot()
			console.log("🧠 Step 2 result - Workspace root:", workspaceRoot)
			if (!workspaceRoot) {
				console.log("🧠 Step 2 FAILED - Workspace root is empty")
				Logger.warn("Workspace root not available, memory service disabled")
				return
			}
			console.log("🧠 Step 2 SUCCESS - Workspace root is available")

			const config = this.getMemoryConfig()
			console.log("🧠 Memory config:", config)
			this.memoryManager = new MemoryManager(workspaceRoot, apiHandler, config)

			console.log("🧠 Memory service initialized successfully")
			Logger.info("Memory service initialized successfully")
		} catch (error) {
			console.error("🧠 Failed to initialize memory service:", error)
			Logger.error("Failed to initialize memory service:", error)
			// Reset memoryManager on failure
			this.memoryManager = null
		}
	}

	/**
	 * Process user input for memory extraction (runs independently of conversation state)
	 */
	public async processUserInput(input: string, context?: string): Promise<void> {
		console.log("🧠 MemoryService.processUserInput() called")
		console.log("🧠 Enabled:", this.isEnabled, "Manager:", !!this.memoryManager)
		console.log("🧠 Input length:", input.length)

		if (!this.isEnabled || !this.memoryManager) {
			console.log("🧠 Memory service not ready for processing")
			return
		}

		try {
			console.log("🧠 Calling memoryManager.processUserInput...")
			// Process in background without blocking or being affected by conversation cancellation
			this.memoryManager.processUserInput(input, context).catch((error) => {
				console.error("🧠 Background memory processing failed:", error)
				Logger.error("Background memory processing failed:", error)
			})
		} catch (error) {
			console.error("🧠 Failed to start memory processing:", error)
			Logger.error("Failed to start memory processing:", error)
		}
	}

	/**
	 * Get memory configuration from VS Code settings
	 */
	private getMemoryConfig(): Partial<MemoryConfig> {
		const config = vscode.workspace.getConfiguration("cline.memory")

		return {
			enabled: config.get<boolean>("enabled", true),
			minInputLength: config.get<number>("minInputLength", 10),
			maxEntriesPerCategory: config.get<number>("maxEntriesPerCategory", 20),
			confidenceThreshold: config.get<number>("confidenceThreshold", 0.6),
			debounceMs: config.get<number>("debounceMs", 2000),
		}
	}

	/**
	 * Load configuration from VS Code settings
	 */
	private loadConfiguration(): void {
		const config = vscode.workspace.getConfiguration("cline.memory")
		this.isEnabled = config.get<boolean>("enabled", true)
		console.log("🧠 Memory service enabled:", this.isEnabled)
	}

	/**
	 * Setup configuration change watcher
	 */
	private setupConfigurationWatcher(): void {
		const disposable = vscode.workspace.onDidChangeConfiguration(async (event) => {
			if (event.affectsConfiguration("cline.memory")) {
				this.loadConfiguration()

				if (this.memoryManager) {
					const newConfig = this.getMemoryConfig()
					this.memoryManager.updateConfig(newConfig)
				}

				// Reinitialize if enabled state changed
				if (this.isEnabled && !this.memoryManager) {
					await this.initialize()
				} else if (!this.isEnabled && this.memoryManager) {
					this.dispose()
				}
			}
		})

		this.disposables.push(disposable)
	}

	/**
	 * Setup workspace change watcher
	 */
	private setupWorkspaceWatcher(): void {
		// Watch for workspace folder changes
		const workspaceFoldersDisposable = vscode.workspace.onDidChangeWorkspaceFolders(async (event) => {
			console.log("🧠 Workspace folders changed:", event)
			console.log("🧠 Added folders:", event.added.length)
			console.log("🧠 Removed folders:", event.removed.length)

			// Clear cache and reinitialize memory service with new workspace
			this.clearWorkspaceCache()
			if (this.isEnabled) {
				await this.initialize()
			}
		})

		this.disposables.push(workspaceFoldersDisposable)
	}

	/**
	 * Setup API handler monitoring for configuration changes
	 */
	private setupApiHandlerMonitoring(): void {
		// Monitor for API configuration changes that might affect the handler
		const apiConfigDisposable = vscode.workspace.onDidChangeConfiguration(async (event) => {
			if (
				event.affectsConfiguration("cline.api") ||
				event.affectsConfiguration("cline.apiKey") ||
				event.affectsConfiguration("cline.apiProvider")
			) {
				console.log("🧠 API configuration changed, checking memory service status...")

				// If memory service was previously failed due to API handler issues, retry
				if (this.isEnabled && !this.isReady()) {
					console.log("🧠 Memory service not ready, attempting reinitialization due to API config change...")
					try {
						await this.initialize()
						if (this.isReady()) {
							console.log("🧠 Memory service successfully reinitialized after API config change!")
						} else {
							console.warn("🧠 Memory service still not ready after API config change")
						}
					} catch (error) {
						console.error("🧠 Failed to reinitialize memory service after API config change:", error)
					}
				}
			}
		})

		this.disposables.push(apiConfigDisposable)
	}

	/**
	 * Get all memories (for debugging/inspection)
	 */
	public getAllMemories() {
		return this.memoryManager?.getAllMemories()
	}

	/**
	 * Clear all memories
	 */
	public async clearMemories(): Promise<void> {
		if (this.memoryManager) {
			await this.memoryManager.clearMemories()
		}
	}

	/**
	 * Check if service is enabled and ready
	 */
	public isReady(): boolean {
		return this.isEnabled && this.memoryManager !== null
	}

	/**
	 * Dispose of resources
	 */
	public dispose(): void {
		console.log("🧠 Disposing memory service...")

		if (this.memoryManager) {
			this.memoryManager.dispose()
			this.memoryManager = null
		}

		this.disposables.forEach((disposable) => disposable.dispose())
		this.disposables = []

		// Clear cache and state
		this.cachedWorkspaceRoot = null
		this.workspaceRootCacheTime = 0
		this.isInitializing = false
		this.initializationPromise = null

		console.log("🧠 Memory service disposed")
	}
}

/**
 * Global memory service instance
 */
let memoryServiceInstance: MemoryService | null = null

/**
 * Initialize global memory service
 */
export function initializeMemoryService(
	context: vscode.ExtensionContext,
	getApiHandler: () => ApiHandler | null,
	getWorkspaceRoot: () => Promise<string>,
): MemoryService {
	if (memoryServiceInstance) {
		memoryServiceInstance.dispose()
	}

	memoryServiceInstance = new MemoryService(context, getApiHandler, getWorkspaceRoot)
	return memoryServiceInstance
}

/**
 * Get global memory service instance
 */
export function getMemoryService(): MemoryService | null {
	return memoryServiceInstance
}

/**
 * Dispose global memory service
 */
export function disposeMemoryService(): void {
	if (memoryServiceInstance) {
		memoryServiceInstance.dispose()
		memoryServiceInstance = null
	}
}
