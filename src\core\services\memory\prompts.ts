import { MemoryCategory, MEMORY_CATEGORIES } from "./types"

/**
 * System prompt for memory classification and summarization
 */
export const MEMORY_CLASSIFICATION_SYSTEM_PROMPT = `你是一个专业的用户输入分析助手。你的任务是分析用户在编程助手对话中的输入内容，将其分类并生成简洁的一句话总结。

## 分类体系

${Object.entries(MEMORY_CATEGORIES)
	.map(
		([category, metadata]) =>
			`### ${metadata.icon} ${metadata.displayName} (${category})
${metadata.description}
示例：
${metadata.examples.map((example) => `- ${example}`).join("\n")}`,
	)
	.join("\n\n")}

## 分析要求

1. **准确分类**：根据用户输入的主要意图选择最合适的分类
2. **简洁总结**：用一句话（不超过100字）总结用户的核心意图或偏好
3. **置信度评估**：评估分类的准确性（0-1之间的数值）
4. **避免重复**：如果内容过于通用或重复，可以归类为general或不处理

## 输出格式

请严格按照以下JSON格式输出：
\`\`\`json
{
  "category": "分类名称",
  "summary": "一句话总结",
  "confidence": 0.85,
  "reasoning": "分类理由（可选）"
}
\`\`\`

## 注意事项

- 只处理有价值的信息，忽略纯粹的问候、感谢等
- 总结要具体且有用，避免过于抽象
- 如果输入内容不适合记录为memory，返回confidence为0
- 重点是用户的明确偏好和需求表达`

/**
 * Generate user prompt for memory classification
 */
export function generateMemoryClassificationPrompt(userInput: string, context?: string): string {
	let prompt = `请分析以下用户输入并进行分类总结：

用户输入：
"""
${userInput}
"""
`

	if (context) {
		prompt += `
上下文信息：
"""
${context}
"""
`
	}

	prompt += `
请根据系统提示中的分类体系和要求，输出JSON格式的分析结果。`

	return prompt
}

/**
 * Prompt for generating category-specific insights
 */
export function generateCategoryInsightPrompt(category: MemoryCategory, entries: string[]): string {
	const metadata = MEMORY_CATEGORIES[category]

	return `基于用户在"${metadata.displayName}"类别下的历史记录，生成一个简洁的洞察总结：

历史记录：
${entries.map((entry, index) => `${index + 1}. ${entry}`).join("\n")}

请生成一个不超过100字的洞察总结，突出用户在${metadata.description}方面的主要特点和偏好。`
}

/**
 * Prompt for memory deduplication
 */
export function generateDeduplicationPrompt(newEntry: string, existingEntries: string[]): string {
	return `判断新的记录是否与现有记录重复或相似：

新记录：
"${newEntry}"

现有记录：
${existingEntries.map((entry, index) => `${index + 1}. ${entry}`).join("\n")}

请回答：
1. 是否重复或高度相似？（是/否）
2. 如果相似，建议如何合并或更新？

输出格式：
\`\`\`json
{
  "isDuplicate": true/false,
  "similarityScore": 0.85,
  "suggestion": "合并建议或保留理由"
}
\`\`\``
}
