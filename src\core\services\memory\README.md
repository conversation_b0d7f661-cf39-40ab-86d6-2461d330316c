# Cline Memories 功能

Cline Memories 是一个智能记忆系统，能够自动学习和记录用户的偏好、习惯和需求，帮助 Cline 更好地理解用户的开发风格。

## 功能特性

### 🎯 智能分类
系统会自动将用户输入分类到以下类别：

- **功能需求** (Feature Requirements): 用户想要实现的具体功能和特性
- **工具偏好** (Tool Preferences): 用户喜欢使用的开发工具、库和框架  
- **技术栈** (Tech Stack): 用户希望使用的编程语言、框架和技术
- **代码风格** (Code Style): 用户的编程习惯、代码规范和风格偏好
- **工作流程** (Workflow): 用户的开发流程、方法论和项目管理偏好
- **问题解决** (Problem Solving): 用户遇到的常见问题、解决方案和调试方法

### 🤖 AI 驱动分析
- 使用当前配置的 AI 模型进行内容分析
- 智能提取用户偏好和习惯
- 生成简洁的一句话总结
- 自动去重避免重复记录

### 📁 文件管理
- 自动创建 `.clinerules/memories.md` 文件
- 按分类组织内容，便于查看和管理
- 支持手动编辑和管理记忆内容

## 配置选项

可以通过 VS Code 设置来配置 Memories 功能：

```json
{
  "cline.memory.enabled": true,                    // 启用/禁用功能
  "cline.memory.minInputLength": 10,               // 最小输入长度
  "cline.memory.maxEntriesPerCategory": 20,        // 每个分类最大条目数
  "cline.memory.confidenceThreshold": 0.6,         // 置信度阈值
  "cline.memory.debounceMs": 2000                  // 防抖延迟时间
}
```

## 工作原理

### 1. 输入监听
- 监听用户在聊天框的输入
- 新任务创建时处理用户输入
- 用户回复消息时处理内容

### 2. 后台处理
- 使用防抖机制避免频繁处理
- 在后台异步分析，不影响正常聊天
- 自动错误处理和日志记录

### 3. 内容分析
- 调用 AI 模型进行内容分类
- 生成简洁的一句话总结
- 评估分类置信度

### 4. 存储管理
- 检查重复内容避免冗余
- 按分类存储到 markdown 文件
- 自动限制每个分类的条目数量

## 文件结构

```
src/core/services/memory/
├── types.ts              # 类型定义和配置
├── prompts.ts            # AI 分析提示词
├── MemoryManager.ts      # 核心内存管理器
├── MemoryService.ts      # VS Code 集成服务
├── index.ts              # 导出模块
├── test/
│   └── MemoryTest.ts     # 功能测试
└── README.md             # 说明文档
```

## 使用示例

### 自动记录功能需求
```
用户输入: "我想要添加一个登录功能"
记录结果: 功能需求 - 用户想要添加登录功能
```

### 自动记录工具偏好
```
用户输入: "我喜欢使用 React 和 TypeScript"
记录结果: 工具偏好 - 用户偏好使用 React 和 TypeScript 开发
```

### 自动记录代码风格
```
用户输入: "请添加详细的注释"
记录结果: 代码风格 - 用户喜欢详细的代码注释
```

## 生成的文件示例

`.clinerules/memories.md` 文件内容示例：

```markdown
# Cline Memories

这个文件记录了用户的偏好、需求和习惯，帮助Cline更好地理解用户的开发风格。

最后更新：2025-01-12 16:30:00

## 🎯 功能需求

- 用户想要添加登录功能
- 需要实现文件上传功能
- 希望有数据可视化图表

## 🔧 工具偏好

- 偏好使用 React 和 TypeScript 开发
- 喜欢使用 VS Code 作为开发环境
- 习惯使用 ESLint 进行代码规范

## 💻 技术栈

- 使用 Node.js 作为后端技术
- 前端采用 React 框架
- 数据库选择 PostgreSQL
```

## 开发和测试

### 运行测试
```bash
# 编译项目
npm run compile

# 运行内存功能测试
node scripts/test-memory.js
```

### 调试
- 查看 VS Code 开发者控制台的日志
- 检查 `.clinerules/memories.md` 文件内容
- 使用 `getMemoryService()` 获取服务实例进行调试

## 注意事项

1. **隐私保护**: 所有记忆数据仅存储在本地，不会上传到服务器
2. **性能影响**: 使用防抖和后台处理，对正常聊天性能影响极小
3. **存储限制**: 自动限制每个分类的条目数量，避免文件过大
4. **错误处理**: 内置完善的错误处理，不会影响主要功能

## 未来改进

- [ ] 支持更多分类类别
- [ ] 添加记忆内容的智能合并
- [ ] 提供记忆内容的搜索功能
- [ ] 支持导出和导入记忆数据
- [ ] 添加记忆内容的可视化展示
